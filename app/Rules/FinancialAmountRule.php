<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class FinancialAmountRule implements ValidationRule
{
    public function __construct(
        private readonly int $maxDigits = 15,
        private readonly int $maxDecimals = 10
    ) {
    }

    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if ($value === null || $value === '') {
            return;
        }

        // Проверяем что это строка или число
        if (!is_string($value) && !is_numeric($value)) {
            $fail('The :attribute must be a valid financial amount.');
            return;
        }

        $stringValue = (string) $value;

        // Проверяем формат числа (может содержать точку для десятичных)
        if (!preg_match('/^-?\d+(\.\d+)?$/', $stringValue)) {
            $fail('The :attribute must be a valid financial amount.');
            return;
        }

        // Проверяем что число не отрицательное (если нужно)
        if (bccomp($stringValue, '0', $this->maxDecimals) < 0) {
            $fail('The :attribute must be greater than or equal to 0.');
            return;
        }

        // Разделяем на целую и дробную части
        $parts = explode('.', $stringValue);
        $integerPart = $parts[0];
        $decimalPart = $parts[1] ?? '';

        // Проверяем максимальное количество цифр в целой части
        if (strlen($integerPart) > $this->maxDigits) {
            $fail("The :attribute cannot have more than {$this->maxDigits} digits.");
            return;
        }

        // Проверяем максимальное количество знаков после запятой
        if (strlen($decimalPart) > $this->maxDecimals) {
            $fail("The :attribute cannot have more than {$this->maxDecimals} decimal places.");
            return;
        }

        // Проверяем максимальное значение (чтобы не было переполнения)
        $maxValue = str_repeat('9', $this->maxDigits) . '.' . str_repeat('9', $this->maxDecimals);
        if (bccomp($stringValue, $maxValue, $this->maxDecimals) > 0) {
            $fail('The :attribute is too large.');
            return;
        }
    }
}
