<?php

namespace App\Traits;

use App\Jobs\FIFOJobs\RecalculationAfterUpdateAcceptanceItemJob;
use App\Jobs\FIFOJobs\RecalculationAfterUpdateShipmentItemJob;
use App\Modules\Marketplaces\Services\Ozon\Actions\Orders\Base\BaseCollectOrderAction;
use App\Modules\Marketplaces\Services\Ozon\Jobs\Orders\BaseOrdersSyncJob;
use App\Modules\Marketplaces\Services\Ozon\Jobs\Reports\SyncFinanceTransactionsJob;
use App\Modules\Marketplaces\Services\Wildberries\Jobs\Reports\SyncReportsJob;
use App\Services\Api\Internal\Procurement\AcceptanceItemsService\AcceptanceItemVatCalculatorService;
use App\Services\Api\Internal\Procurement\AcceptanceItemsService\Handlers\AcceptanceItemDeleteHandler;
use App\Services\Api\Internal\Procurement\AcceptanceItemsService\Handlers\AcceptanceItemsCreateHandler;
use App\Services\Api\Internal\Procurement\AcceptanceItemsService\Handlers\AcceptanceItemsUpdateHandler;
use App\Services\Api\Internal\Procurement\VendorOrders\VendorOrderItemsService\Handlers\VendorOrderItemCreateHandler;
use App\Services\Api\Internal\Procurement\VendorOrders\VendorOrderItemsService\Handlers\VendorOrderItemDeleteHandler;
use App\Services\Api\Internal\Procurement\VendorOrders\VendorOrderItemsService\Handlers\VendorOrderItemUpdateHandler;
use App\Services\Api\Internal\Procurement\VendorOrders\VendorOrderItemsService\VendorOrderItemVatCalculatorService;
use App\Services\Api\Internal\Sales\ComissionReports\IssuedReports\Items\IssuedComissionReportsItemsService;
use App\Services\Api\Internal\Sales\ComissionReports\ReceivedReports\Items\ReceivedComissionReportsRealizedItemsService;
use App\Services\Api\Internal\Sales\ComissionReports\ReceivedReports\Items\ReceivedComissionReportsReturnItemsService;
use App\Services\Api\Internal\Sales\CustomerOrders\CustomerOrderItemsService\CustomerOrderItemVatCalculatorService;
use App\Services\Api\Internal\Sales\CustomerOrders\CustomerOrderItemsService\Handlers\CustomerOrderItemCreateHandler;
use App\Services\Api\Internal\Sales\CustomerOrders\CustomerOrderItemsService\Handlers\CustomerOrderItemDeleteHandler;
use App\Services\Api\Internal\Sales\CustomerOrders\CustomerOrderItemsService\Handlers\CustomerOrderItemUpdateHandler;
use App\Services\Api\Internal\Sales\Shipments\ShipmentItemsService\Handlers\ShipmentItemsCreateHandler;
use App\Services\Api\Internal\Sales\Shipments\ShipmentItemsService\Handlers\ShipmentItemsUpdateHandler;

/**
 * Трейт для точных денежных и числовых расчетов с использованием BcMath
 * Обеспечивает максимальную точность для бухгалтерских расчетов
 * Все значения передаются и возвращаются как строки для максимальной точности
 */
trait PrecisionCalculator
{
    /**
     * Максимальная точность расчетов (50 знаков после запятой для максимальной точности)
     */
    protected int $precision = 50;

    /**
     * Точность для отображения денежных сумм (2 знака после запятой)
     */
    protected int $displayPrecision = 2;

    /**
     * Преобразование числа в строку для BcMath
     *
     * @param float|int|string $number Число для преобразования
     * @return string Число в виде строки
     * @throws \InvalidArgumentException Если передан неподдерживаемый тип
     */
    protected function toStringNumber(float|int|string $number): string
    {
        if (is_string($number)) {
            return $number;
        }

        if (is_int($number)) {
            return (string) $number;
        }

        if (is_float($number)) {
            // Для float используем более умный подход
            // Сначала пробуем простое приведение к строке
            $simple = (string) $number;

            // Если нет научной нотации, используем простое приведение
            if (strpos($simple, 'E') === false && strpos($simple, 'e') === false) {
                return $this->normalize($simple);
            }

            // Иначе используем sprintf с нормализацией
            $result = sprintf('%.50F', $number);
            return $this->normalize($result);
        }

        throw new \InvalidArgumentException('Поддерживаются только string, int и float типы');
    }

    /**
     * Сложение двух чисел
     *
     * @param float|int|string $left Первое число
     * @param float|int|string $right Второе число
     * @param int|null $scale Точность (по умолчанию используется $this->precision)
     * @return string Результат сложения
     */
    public function add(float|int|string $left, float|int|string $right, ?int $scale = null): string
    {
        $result = bcadd(
            $this->toStringNumber($left),
            $this->toStringNumber($right),
            $scale ?? $this->precision
        );
        return $this->normalize($result);
    }

    /**
     * Вычитание двух чисел
     *
     * @param float|int|string $left Уменьшаемое
     * @param float|int|string $right Вычитаемое
     * @param int|null $scale Точность (по умолчанию используется $this->precision)
     * @return string Результат вычитания
     */
    public function subtract(float|int|string $left, float|int|string $right, ?int $scale = null): string
    {
        $result = bcsub(
            $this->toStringNumber($left),
            $this->toStringNumber($right),
            $scale ?? $this->precision
        );
        return $this->normalize($result);
    }

    /**
     * Умножение двух чисел
     *
     * @param float|int|string $left Первый множитель
     * @param float|int|string $right Второй множитель
     * @param int|null $scale Точность (по умолчанию используется $this->precision)
     * @return string Результат умножения
     */
    public function multiply(float|int|string $left, float|int|string $right, ?int $scale = null): string
    {
        $result = bcmul(
            $this->toStringNumber($left),
            $this->toStringNumber($right),
            $scale ?? $this->precision
        );
        return $this->normalize($result);
    }

    /**
     * Деление двух чисел
     *
     * @param string|int|float $dividend Делимое
     * @param string|int|float $divisor Делитель
     * @param int|null $scale Точность (по умолчанию используется $this->precision)
     * @return string Результат деления
     * @throws \InvalidArgumentException Если делитель равен нулю
     */
    public function divide($dividend, $divisor, ?int $scale = null): string
    {
        $divisorStr = $this->toStringNumber($divisor);
        if (bccomp($divisorStr, '0', $this->precision) === 0) {
            throw new \InvalidArgumentException('Деление на ноль невозможно');
        }

        $result = bcdiv(
            $this->toStringNumber($dividend),
            $divisorStr,
            $scale ?? $this->precision
        );
        return $this->normalize($result);
    }

    /**
     * Возведение в степень
     *
     * @param string|int|float $base Основание
     * @param string|int|float $exponent Показатель степени
     * @param int|null $scale Точность (по умолчанию используется $this->precision)
     * @return string Результат возведения в степень
     */
    public function power($base, $exponent, ?int $scale = null): string
    {
        $result = bcpow(
            $this->toStringNumber($base),
            $this->toStringNumber($exponent),
            $scale ?? $this->precision
        );
        return $this->normalize($result);
    }

    /**
     * Извлечение квадратного корня
     *
     * @param string|int|float $operand Число для извлечения корня
     * @param int|null $scale Точность (по умолчанию используется $this->precision)
     * @return string Квадратный корень
     * @throws \InvalidArgumentException Если число отрицательное
     */
    public function sqrt($operand, ?int $scale = null): string
    {
        $operandStr = $this->toStringNumber($operand);
        if (bccomp($operandStr, '0', $this->precision) < 0) {
            throw new \InvalidArgumentException('Нельзя извлечь корень из отрицательного числа');
        }

        $result = bcsqrt($operandStr, $scale ?? $this->precision);
        return $this->normalize($result);
    }

    /**
     * Сравнение двух чисел
     *
     * @param float|int|string $left Первое число
     * @param float|int|string $right Второе число
     * @param int|null $scale Точность (по умолчанию используется $this->precision)
     * @return int -1 если left < right, 0 если равны, 1 если left > right
     */
    public function compare(float|int|string $left, float|int|string $right, ?int $scale = null): int
    {
        return bccomp(
            $this->toStringNumber($left),
            $this->toStringNumber($right),
            $scale ?? $this->precision
        );
    }

    /**
     * Проверка равенства двух чисел
     *
     * @param float|int|string $left Первое число
     * @param float|int|string $right Второе число
     * @param int|null $scale Точность (по умолчанию используется $this->precision)
     * @return bool true если числа равны
     */
    public function equals(float|int|string $left, float|int|string $right, ?int $scale = null): bool
    {
        return $this->compare($left, $right, $scale) === 0;
    }

    /**
     * Проверка, больше ли первое число второго
     *
     * @param float|int|string $left Первое число
     * @param float|int|string $right Второе число
     * @param int|null $scale Точность (по умолчанию используется $this->precision)
     * @return bool true если left > right
     */
    public function greaterThan(float|int|string $left, float|int|string $right, ?int $scale = null): bool
    {
        return $this->compare($left, $right, $scale) > 0;
    }

    /**
     * Проверка, меньше ли первое число второго
     *
     * @param float|int|string $left Первое число
     * @param float|int|string $right Второе число
     * @param int|null $scale Точность (по умолчанию используется $this->precision)
     * @return bool true если left < right
     */
    public function lessThan(float|int|string $left, float|int|string $right, ?int $scale = null): bool
    {
        return $this->compare($left, $right, $scale) < 0;
    }

    /**
     * Конвертация рублей в копейки
     *
     * @param float|int|string $rubles Сумма в рублях
     * @return string Сумма в копейках
     */
    public function rublesToKopecks(float|int|string $rubles): string
    {
        return $this->multiply($rubles, 100);
    }

    /**
     * Конвертация копеек в рубли
     *
     * @param float|int|string $kopecks Сумма в копейках
     * @return string Сумма в рублях
     */
    public function kopecksToRubles(float|int|string $kopecks): string
    {
        return $this->divide($kopecks, 100);
    }

    /**
     * Расчет процента от суммы
     *
     * @param float|int|string $amount Сумма
     * @param float|int|string $percentage Процент
     * @return string Результат расчета процента
     */
    public function calculatePercentage(float|int|string $amount, float|int|string $percentage): string
    {
        return $this->divide(
            $this->multiply($amount, $percentage),
            100
        );
    }

    /**
     * Применение скидки к сумме
     *
     * @param float|int|string $amount Исходная сумма
     * @param float|int|string $discountPercentage Процент скидки
     * @return string Сумма после применения скидки
     */
    public function applyDiscount(float|int|string $amount, float|int|string $discountPercentage): string
    {
        $discountAmount = $this->calculatePercentage($amount, $discountPercentage);
        return $this->subtract($amount, $discountAmount);
    }

    /**
     * Добавление процента к сумме (например, НДС)
     *
     * @param float|int|string $amount Исходная сумма
     * @param float|int|string $percentage Процент для добавления
     * @return string Сумма с добавленным процентом
     */
    public function addPercentage(float|int|string $amount, float|int|string $percentage): string
    {
        $additionalAmount = $this->calculatePercentage($amount, $percentage);
        return $this->add($amount, $additionalAmount);
    }

    /**
     * Округление числа до заданной точности
     *
     * @param string $number Число для округления
     * @param int $scale Количество знаков после запятой
     * @param int $mode Режим округления (PHP_ROUND_HALF_UP по умолчанию)
     * @return string Округленное число
     */
    public function round(string $number, int $scale = 2, int $mode = PHP_ROUND_HALF_UP): string
    {
        // Для максимальной точности используем BcMath подход
        $factor = bcpow('10', (string) $scale, 0);
        $multiplied = bcmul($number, $factor, $this->precision);

        // Добавляем 0.5 для округления вверх при .5
        if ($mode === PHP_ROUND_HALF_UP) {
            $multiplied = bcadd($multiplied, '0.5', $this->precision);
        }

        // Отбрасываем дробную часть
        $truncated = bcadd($multiplied, '0', 0);

        return bcdiv($truncated, $factor, $scale);
    }

    /**
     * Форматирование числа для отображения денежной суммы
     *
     * @param string $amount Сумма
     * @param int|null $decimals Количество знаков после запятой (по умолчанию 2)
     * @param string $decimalSeparator Разделитель дробной части
     * @param string $thousandsSeparator Разделитель тысяч
     * @return string Отформатированная сумма
     */
    public function formatMoney(
        string $amount,
        ?int $decimals = null,
        string $decimalSeparator = '.',
        string $thousandsSeparator = ' '
    ): string {
        $decimals = $decimals ?? $this->displayPrecision;
        $rounded = $this->round($amount, $decimals);
        return number_format((float) $rounded, $decimals, $decimalSeparator, $thousandsSeparator);
    }

    /**
     * Получение абсолютного значения числа
     *
     * @param string $number Число
     * @return string Абсолютное значение
     */
    public function abs(string $number): string
    {
        return $this->lessThan($number, '0') ? $this->multiply($number, '-1') : $number;
    }

    /**
     * Проверка, является ли число нулем
     *
     * @param string $number Число для проверки
     * @return bool true если число равно нулю
     */
    public function isZero(string $number): bool
    {
        return $this->equals($number, '0');
    }

    /**
     * Проверка, является ли число положительным
     *
     * @param string $number Число для проверки
     * @return bool true если число больше нуля
     */
    public function isPositive(string $number): bool
    {
        return $this->greaterThan($number, '0');
    }

    /**
     * Проверка, является ли число отрицательным
     *
     * @param string $number Число для проверки
     * @return bool true если число меньше нуля
     */
    public function isNegative(string $number): bool
    {
        return $this->lessThan($number, '0');
    }

    /**
     * Установка точности расчетов
     *
     * @param int $precision Новая точность
     * @return CustomerOrderItemCreateHandler|RecalculationAfterUpdateAcceptanceItemJob|RecalculationAfterUpdateShipmentItemJob|BaseCollectOrderAction|BaseOrdersSyncJob|SyncFinanceTransactionsJob|\App\Modules\Marketplaces\Services\Wildberries\Jobs\Orders\Base\BaseOrdersSyncJob|SyncReportsJob|AcceptanceItemVatCalculatorService|AcceptanceItemDeleteHandler|AcceptanceItemsCreateHandler|AcceptanceItemsUpdateHandler|VendorOrderItemCreateHandler|VendorOrderItemDeleteHandler|VendorOrderItemUpdateHandler|VendorOrderItemVatCalculatorService|IssuedComissionReportsItemsService|ReceivedComissionReportsRealizedItemsService|ReceivedComissionReportsReturnItemsService|CustomerOrderItemVatCalculatorService|CustomerOrderItemDeleteHandler|CustomerOrderItemUpdateHandler|ShipmentItemsCreateHandler|ShipmentItemsUpdateHandler|PrecisionCalculator
     */
    public function setPrecision(int $precision): self
    {
        $this->precision = $precision;
        return $this;
    }

    /**
     * Получение текущей точности расчетов
     *
     * @return int Текущая точность
     */
    public function getPrecision(): int
    {
        return $this->precision;
    }

    /**
     * Расчет суммы с НДС
     *
     * @param string $amountWithoutVat Сумма без НДС
     * @param string $vatRate Ставка НДС (например, "20" для 20%)
     * @return array Массив с суммой без НДС, суммой НДС и общей суммой
     */
    public function calculateVat(string $amountWithoutVat, string $vatRate): array
    {
        $vatAmount = $this->calculatePercentage($amountWithoutVat, $vatRate);
        $totalAmount = $this->add($amountWithoutVat, $vatAmount);

        return [
            'amount_without_vat' => $amountWithoutVat,
            'vat_amount' => $vatAmount,
            'total_amount' => $totalAmount,
            'vat_rate' => $vatRate
        ];
    }

    /**
     * Выделение НДС из суммы с НДС
     *
     * @param string $amountWithVat Сумма с НДС
     * @param string $vatRate Ставка НДС (например, "20" для 20%)
     * @return array Массив с суммой без НДС, суммой НДС и общей суммой
     */
    public function extractVat(string $amountWithVat, string $vatRate): array
    {
        $divisor = $this->add('100', $vatRate);
        $amountWithoutVat = $this->divide(
            $this->multiply($amountWithVat, '100'),
            $divisor
        );
        $vatAmount = $this->subtract($amountWithVat, $amountWithoutVat);

        return [
            'amount_without_vat' => $amountWithoutVat,
            'vat_amount' => $vatAmount,
            'total_amount' => $amountWithVat,
            'vat_rate' => $vatRate
        ];
    }

    /**
     * Расчет общей суммы массива значений
     *
     * @param array $amounts Массив сумм (string|int|float)
     * @return string Общая сумма
     */
    public function sum(array $amounts): string
    {
        $total = '0';
        foreach ($amounts as $amount) {
            $total = $this->add($total, $amount);
        }
        return $total;
    }

    /**
     * Расчет среднего значения массива
     *
     * @param array $amounts Массив сумм (string|int|float)
     * @return string Среднее значение
     * @throws \InvalidArgumentException Если массив пустой
     */
    public function average(array $amounts): string
    {
        if (empty($amounts)) {
            throw new \InvalidArgumentException('Массив не может быть пустым');
        }

        $total = $this->sum($amounts);
        $count = count($amounts);

        return $this->divide($total, $count);
    }

    /**
     * Поиск минимального значения в массиве
     *
     * @param array $amounts Массив сумм (string|int|float)
     * @return string Минимальное значение
     * @throws \InvalidArgumentException Если массив пустой
     */
    public function min(array $amounts): string
    {
        if (empty($amounts)) {
            throw new \InvalidArgumentException('Массив не может быть пустым');
        }

        $min = $this->toStringNumber(array_shift($amounts));
        foreach ($amounts as $amount) {
            if ($this->lessThan($amount, $min)) {
                $min = $this->toStringNumber($amount);
            }
        }

        return $min;
    }

    /**
     * Поиск максимального значения в массиве
     *
     * @param array $amounts Массив сумм (string|int|float)
     * @return string Максимальное значение
     * @throws \InvalidArgumentException Если массив пустой
     */
    public function max(array $amounts): string
    {
        if (empty($amounts)) {
            throw new \InvalidArgumentException('Массив не может быть пустым');
        }

        $max = $this->toStringNumber(array_shift($amounts));
        foreach ($amounts as $amount) {
            if ($this->greaterThan($amount, $max)) {
                $max = $this->toStringNumber($amount);
            }
        }

        return $max;
    }

    /**
     * Расчет сложных процентов
     *
     * @param string $principal Основная сумма
     * @param string $rate Процентная ставка (в процентах)
     * @param string $periods Количество периодов
     * @return string Итоговая сумма со сложными процентами
     */
    public function compoundInterest(string $principal, string $rate, string $periods): string
    {
        // A = P(1 + r/100)^n
        $rateDecimal = $this->divide($rate, '100');
        $onePlusRate = $this->add('1', $rateDecimal);
        $compound = $this->power($onePlusRate, $periods);

        return $this->multiply($principal, $compound);
    }

    /**
     * Проверка валидности числовой строки
     *
     * @param string $number Строка для проверки
     * @return bool true если строка является валидным числом
     */
    public function isValidNumber(string $number): bool
    {
        return is_numeric($number);
    }

    /**
     * Нормализация числовой строки (удаление лишних нулей)
     *
     * @param string $number Число для нормализации
     * @return string Нормализованное число
     */
    public function normalize(string $number): string
    {
        // Удаляем лишние нули справа после запятой
        $normalized = rtrim($number, '0');
        // Если остался только разделитель, удаляем и его
        $normalized = rtrim($normalized, '.');
        // Если строка пустая, возвращаем 0
        return $normalized === '' ? '0' : $normalized;
    }
}
