<?php

namespace App\Http\Requests\Api\Internal\CustomerOrderItems;

use App\Contracts\Requests\ToDtoContract;
use App\Rules\FinancialAmountRule;
use App\Services\Api\Internal\Sales\CustomerOrders\CustomerOrderItemsService\DTO\CustomerOrderItemDTO;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;

class CustomerOrderItemUpdateRequest extends FormRequest implements ToDtoContract
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'quantity' => 'required|integer|min:1',
            'discount' => ['nullable', new FinancialAmountRule()],
            'total_price' => ['nullable', new FinancialAmountRule()],
            'vat_rate_id' => 'nullable|UUID',
            'currency_id' => 'nullable|UUID',
            'price_in_currency' => ['nullable', new FinancialAmountRule()],
            'currency_rate_to_base' => ['nullable', new FinancialAmountRule()],
        ];
    }

    public function toDTO(): CustomerOrderItemDTO
    {
        return CustomerOrderItemDTO::fromArray(
            array_merge($this->validated(), ['id' => $this->route('id')])
        );
    }
}
