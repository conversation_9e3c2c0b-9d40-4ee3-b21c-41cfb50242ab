<?php

namespace App\Services\Api\Internal\Procurement\VendorOrders\VendorOrderItemsService\DTO;

use App\Contracts\DtoContract;
use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\HasUpdateArrayDtoContract;
use App\Traits\HasOrderedUuid;

class VendorOrderItemDTO implements DtoContract, HasInsertArrayDtoContract, HasUpdateArrayDtoContract
{
    use HasOrderedUuid;
    public function __construct(
        public ?string $orderId,
        public ?string $productId,
        public ?int $quantity,
        public ?string $priceInCurrency,
        public ?string $currencyRateToBase,
        public ?string $currencyId,
        public ?string $discount = '0',
        public ?string $vatRateId = null,
        public ?string $resourceId = null,
        public ?string $priceInBase = null,
        public ?string $amountInBase = null,
    ) {
    }


    public static function fromArray(array $data): self
    {
        $dto = new self(
            orderId: $data['order_id'] ?? null,
            productId: $data['product_id'] ?? null,
            quantity: $data['quantity'] ?? null,
            priceInCurrency: $data['price_in_currency'] ?? null,
            currencyRateToBase: $data['currency_rate_to_base'] ?? null,
            currencyId: $data['currency_id'] ?? null,
            discount: $data['discount'] ?? null,
            vatRateId: $data['vat_rate_id'] ?? null,
            resourceId: $data['id'] ?? null,
            priceInBase: $data['price_in_base'] ?? null,
            amountInBase: $data['amount_in_base'] ?? null,
        );

        // Пересчитываем total_price после создания, так как теперь у нас есть все данные
        $dto->totalPrice = $dto->calculateTotalPrice();

        return $dto;
    }

    public function toInsertArray(string $id, ?string $employeeId = null): array
    {
        return [
            'id' => $id,
            'order_id' => $this->orderId,
            'product_id' => $this->productId,
            'quantity' => $this->quantity,
            'discount' => $this->discount,
            'vat_rate_id' => $this->vatRateId,
            'currency_id' => $this->currencyId,
            'price_in_currency' => $this->priceInCurrency,
            'currency_rate_to_base' => $this->currencyRateToBase,
            'price_in_base' => $this->priceInBase,
            'amount_in_base' => $this->amountInBase,
        ];
    }

    public function toUpdateArray(): array
    {
        return [
            'quantity' => $this->quantity,
            'vat_rate_id' => $this->vatRateId,
            'discount' => $this->discount,
            'currency_id' => $this->currencyId,
            'price_in_currency' => $this->priceInCurrency,
            'currency_rate_to_base' => $this->currencyRateToBase,
            'price_in_base' => $this->priceInBase,
            'amount_in_base' => $this->amountInBase,
        ];
    }
}
