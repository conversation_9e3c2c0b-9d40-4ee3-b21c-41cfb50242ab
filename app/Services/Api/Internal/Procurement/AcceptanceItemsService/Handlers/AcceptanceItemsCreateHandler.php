<?php

namespace App\Services\Api\Internal\Procurement\AcceptanceItemsService\Handlers;

use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\Repositories\AcceptanceItemsRepositoryContract;
use App\Contracts\Repositories\AcceptanceRepositoryContract;
use App\Contracts\Repositories\ShipmentItemsRepositoryContract;
use App\Contracts\Repositories\VatRatesRepositoryContract;
use App\Contracts\Repositories\WarehouseItemsRepositoryContract;
use App\Jobs\FIFOJobs\HandleFifoJob;
use App\Services\Api\Internal\Procurement\Acceptances\Traits\AcceptanceTotalCalculator;
use App\Services\Api\Internal\Procurement\AcceptanceItemsService\DTO\AcceptanceItemDto;
use App\Traits\HasOrderedUuid;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\Bus;
use RuntimeException;

class AcceptanceItemsCreateHandler
{
    use HasOrderedUuid;
    use AcceptanceTotalCalculator;

    private HasInsertArrayDtoContract $dto;
    private string $resourceId;

    public function __construct(
        private readonly AcceptanceItemsRepositoryContract $acceptanceItemsRepository,
        private readonly WarehouseItemsRepositoryContract $warehouseItemsRepository,
        private readonly AcceptanceRepositoryContract $acceptanceRepository,
        private readonly ShipmentItemsRepositoryContract $shipmentItemsRepository,
        private readonly VatRatesRepositoryContract $vatRatesRepository
    ) {
        $this->resourceId = $this->generateUuid();
    }

    /**
     * @throws Exception
     */
    public function run(HasInsertArrayDtoContract $dto): string
    {
        if (!$dto instanceof AcceptanceItemDto) {
            throw new RuntimeException('Invalid DTO type');
        }

        $this->dto = $dto;

        $checkItemExists = $this->acceptanceItemsRepository
            ->checkItemExists($this->dto->productId, $this->dto->acceptanceId);

        if ($checkItemExists) {
            throw new RuntimeException('Item already exists');
        }

        $acceptance = $this->acceptanceRepository->show($this->dto->acceptanceId);

        if (!$acceptance) {
            throw new RuntimeException('Acceptance not found');
        }

        // Если has_vat = false, автоматически выбираем ставку "Без НДС"
        if ($acceptance->has_vat === false && $this->dto->vat_rate_id === null) {
            $noVatRate = $this->vatRatesRepository->getByRateAndCabinet(0, $acceptance->cabinet_id);
            if ($noVatRate) {
                $this->dto->vat_rate_id = $noVatRate->id;
            }
        }

        // Рассчитываем total_price и total_vat_sum
        $calculatedData = $this->calculateAcceptanceItemTotals($this->dto, $acceptance);

        $insertData = $this->dto->toInsertArray($this->resourceId);
        $insertData['total_price'] = $calculatedData['total_price'];
        $insertData['total_vat_sum'] = $calculatedData['total_vat_sum'];

        $this->acceptanceItemsRepository->insert($insertData);

        // Пересчитываем общую сумму приемки с учетом НДС
        $this->updateAcceptanceTotal($this->dto->acceptanceId);

        $this->warehouseItemsRepository->create(
            [
                'id' => $this->generateUuid(),
                'created_at' => Carbon::now(),
                'warehouse_id' => $acceptance->warehouse_id,
                'product_id' => $this->dto->productId,
                'quantity' => $this->dto->quantity,
                'acceptance_id' => $this->dto->acceptanceId,
                'batch_number' => $acceptance->number,
                'unit_price' => $this->dto->price,
                'total_price' => $this->dto->totalPrice,
                'received_at' => $acceptance->date_from,
                'status' => 'in_stock',
            ]
        );


        $meleeItem = $this->warehouseItemsRepository
            ->findFirstShipmentItemByProductWarehouseAndDate(
                $this->dto->productId,
                $acceptance->warehouse_id,
                $acceptance->date_from
            );

        if (!$meleeItem) {
            $meleeItem = $this->shipmentItemsRepository
                ->findUncompletedOrPendingShipmentItemByProductWarehouseAndDate(
                    $this->dto->productId,
                    $acceptance->warehouse_id,
                    $acceptance->date_from
                );
        }

        if ($meleeItem) {
            Bus::dispatch(new HandleFifoJob($meleeItem->shipment_item_id));
        }

        return $this->resourceId;
    }

    private function calculateAcceptanceItemTotals(AcceptanceItemDto $dto, object $acceptance): array
    {
        // Получаем ставку НДС
        $vatRate = '0';
        if ($dto->vat_rate_id) {
            $vatRateEntity = $this->vatRatesRepository->show($dto->vat_rate_id);
            $vatRate = $vatRateEntity ? (string)$vatRateEntity->rate : '0';
        }

        // Базовая сумма = цена × количество
        $baseAmount = $this->multiply($dto->price, (string)$dto->quantity);

        // Применяем скидку
        $amountAfterDiscount = $this->applyDiscount($baseAmount, $dto->discount);

        // Рассчитываем НДС в зависимости от настроек
        if (!$acceptance->has_vat || $this->compare($vatRate, '0') == 0) {
            // Без НДС
            $totalPrice = $amountAfterDiscount;
            $totalVatSum = '0';
        } elseif ($acceptance->price_includes_vat) {
            // НДС включен в цену
            $vatCalculation = $this->extractVat($amountAfterDiscount, $vatRate);
            $totalPrice = $amountAfterDiscount;
            $totalVatSum = $vatCalculation['vat_amount'];
        } else {
            // НДС сверх цены
            $vatCalculation = $this->calculateVat($amountAfterDiscount, $vatRate);
            $totalPrice = $vatCalculation['total_amount'];
            $totalVatSum = $vatCalculation['vat_amount'];
        }

        return [
            'total_price' => $totalPrice,
            'total_vat_sum' => $totalVatSum
        ];
    }
}
