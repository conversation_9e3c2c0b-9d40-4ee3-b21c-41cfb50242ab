<?php

namespace App\Services\Api\Internal\Procurement\AcceptanceItemsService\DTO;

use App\Contracts\DtoContract;
use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\HasUpdateArrayDtoContract;

class AcceptanceItemDto implements DtoContract, HasInsertArrayDtoContract, HasUpdateArrayDtoContract
{
    public function __construct(
        public string $quantity,
        public ?string $acceptanceId = null,
        public ?string $productId = null,
        public string $price = '0',
        public string $discount = '0',
        public ?string $vat_rate_id = null,
        public ?string $countryId = null,
        public ?string $gtdNumber = null,
        public ?string $resourceId = null
    ) {
    }



    public function toInsertArray(string $id, ?string $employeeId = null): array
    {
        return [
            'id' => $id,
            'acceptance_id' => $this->acceptanceId,
            'product_id' => $this->productId,
            'quantity' => $this->quantity,
            'price' => $this->price,
            'discount' => $this->discount,
            'vat_rate_id' => $this->vat_rate_id,
            'country_id' => $this->countryId,
            'gtd_number' => $this->gtdNumber,
            ];
    }

    public function toUpdateArray(): array
    {
        return [
            'quantity' => $this->quantity,
            'price' => $this->price,
            'discount' => $this->discount,
            'vat_rate_id' => $this->vat_rate_id,
            'country_id' => $this->countryId,
            'gtd_number' => $this->gtdNumber,
        ];
    }

    public static function fromArray(array $data): self
    {
        return new self(
            quantity: (string)($data['quantity'] ?? '1'),
            acceptanceId: $data['acceptance_id'] ?? null,
            productId: $data['product_id'] ?? null,
            price: (string)($data['price'] ?? '0'),
            discount: (string)($data['discount'] ?? '0'),
            vat_rate_id: $data['vat_rate_id'] ?? null,
            countryId: $data['country_id'] ?? null,
            gtdNumber: $data['gtd_number'] ?? null,
            resourceId: $data['id'] ?? null,
        );
    }
}
