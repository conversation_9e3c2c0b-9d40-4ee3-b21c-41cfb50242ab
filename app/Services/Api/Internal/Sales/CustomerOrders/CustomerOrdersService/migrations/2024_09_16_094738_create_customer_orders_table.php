<?php

namespace App\Services\Api\Internal\Sales\CustomerOrders\CustomerOrdersService\migrations;

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('customer_orders', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->timestamps();

            $table->foreignUuid('cabinet_id')->constrained()->cascadeOnDelete();
            $table->foreignUuid('employee_id')->constrained();
            $table->foreignUuid('department_id')->constrained();
            $table->boolean('is_common')->default(false);

            $table->string('number');
            $table->dateTime('date_from')->nullable();
            $table->string('payment_status')->default('unpaid');
            $table->foreignUuid('status_id')->nullable()->constrained()->nullOnDelete();
            $table->boolean('held')->default(false);
            $table->boolean('reserve')->default(false);

            $table->foreignUuid('legal_entity_id')->references('id')->on('legal_entities');
            $table->foreignUuid('contractor_id')->references('id')->on('contractors');
            $table->date('plan_date')->nullable();
            $table->foreignUuid('sales_channel_id')->nullable()->references('id')->on('sales_channels');
            $table->foreignUuid('warehouse_id')->nullable()->references('id')->on('warehouses');

            $table->string('total_price')->default('0');

            $table->text('comment')->nullable();

            $table->softDeletes();
        });

        Schema::create('customer_order_items', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->timestamps();

            $table->foreignUuid('order_id')->references('id')->on('customer_orders')->cascadeOnDelete();
            $table->foreignUuid('product_id')->references('id')->on('products');
            $table->unsignedBigInteger('quantity');

            $table->foreignUuid('vat_rate_id')->nullable()->references('id')->on('vat_rates');
            $table->string('discount')->default('0');

            $table->foreignUuid('currency_id')->references('id')->on('cabinet_currencies');
            $table->string('price_in_currency')->default('0');
            $table->string('currency_rate_to_base')->default('1');
            $table->string('price_in_base')->default('0');
            $table->string('amount_in_base')->default('0');
            $table->string('total_vat_sum')->default('0');
        });

        Schema::create('customer_order_delivery_infos', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->timestamps();

            $table->foreignUuid('order_id')->unique()->references('id')->on('customer_orders')->cascadeOnDelete();
            $table->text('comment')->nullable();
            $table->string('post_code')->nullable();
            $table->string('country')->nullable();
            $table->string('region')->nullable();
            $table->string('city')->nullable();
            $table->string('street')->nullable();
            $table->string('house')->nullable();
            $table->string('office')->nullable();
            $table->string('other')->nullable();

            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('customer_order_delivery_infos');
        Schema::dropIfExists('customer_order_items');
        Schema::dropIfExists('customer_orders');
    }
};
