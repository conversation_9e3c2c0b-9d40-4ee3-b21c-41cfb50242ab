<?php

namespace App\Services\Api\Internal\Sales\Shipments\ShipmentItemsService\DTO;

use App\Contracts\DtoContract;
use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\HasUpdateArrayDtoContract;
use App\Traits\HasOrderedUuid;

class ShipmentItemDTO implements DtoContract, HasInsertArrayDtoContract, HasUpdateArrayDtoContract
{
    use HasOrderedUuid;

    public function __construct(
        public ?string $cabinetId,
        public ?string $shipmentId,
        public ?string $productId,
        public ?string $vat_rate_id,
        public string $price = '0',
        public string $discount = '0',
        public string $quantity = '1',
        public ?string $resourceId = null,
        public string $cost = '0'
    ) {
    }



    public function toInsertArray(string $id, ?string $employeeId = null): array
    {
        return [
            'id' => $id,
            'shipment_id' => $this->shipmentId,
            'product_id' => $this->productId,
            'quantity' => $this->quantity,
            'price' => $this->price,
            'discount' => $this->discount,
            'vat_rate_id' => $this->vat_rate_id,
            'cost' => $this->cost
        ];
    }

    public function toUpdateArray(): array
    {
        return [
            'quantity' => $this->quantity,
            'price' => $this->price,
            'discount' => $this->discount,
            'vat_rate_id' => $this->vat_rate_id,
            'cost' => $this->cost
        ];
    }

    public static function fromArray(array $data): self
    {
        return new self(
            cabinetId: $data['cabinet_id'] ?? null,
            shipmentId: $data['shipment_id'] ?? null,
            productId: $data['product_id'] ?? null,
            vat_rate_id: $data['vat_rate_id'] ?? null,
            price: (string)($data['price'] ?? '0'),
            discount: (string)($data['discount'] ?? '0'),
            quantity: (string)($data['quantity'] ?? '1'),
            resourceId: $data['id'] ?? null,
            cost: (string)($data['cost'] ?? '0')
        );
    }
}
