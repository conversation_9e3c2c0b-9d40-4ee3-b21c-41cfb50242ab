<?php

namespace App\Services\Api\Internal\Sales\Shipments\ShipmentItemsService\Handlers;

use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Repositories\ShipmentItemsRepositoryContract;
use App\Contracts\Repositories\ShipmentsRepositoryContract;
use App\Contracts\Repositories\VatRatesRepositoryContract;
use App\Jobs\FIFOJobs\RecalculationAfterUpdateShipmentItemJob;
use App\Services\Api\Internal\Sales\Shipments\ShipmentItemsService\DTO\ShipmentItemDTO;
use App\Traits\HasOrderedUuid;
use App\Traits\PrecisionCalculator;
use http\Exception\InvalidArgumentException;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Support\Facades\Queue;
use Symfony\Component\Routing\Exception\ResourceNotFoundException;

class ShipmentItemsUpdateHandler
{
    use HasOrderedUuid;
    use PrecisionCalculator;

    public function __construct(
        private readonly ShipmentItemsRepositoryContract $shipmentItemsRepository,
        private readonly ShipmentsRepositoryContract $shipmentsRepository,
        private readonly VatRatesRepositoryContract $vatRatesRepository
    ) {
    }

    /**
     * @throws BindingResolutionException
     */
    public function run(HasUpdateArrayDtoContract $dto): void
    {
        if (!$dto instanceof ShipmentItemDTO) {
            throw new InvalidArgumentException();
        }
        $item = $this->shipmentItemsRepository->show($dto->resourceId);

        if (!$item) {
            throw new ResourceNotFoundException();
        }

        // Получаем отгрузку для расчета НДС
        $shipment = $this->shipmentsRepository->show($item->shipment_id);
        if (!$shipment) {
            throw new ResourceNotFoundException('Shipment not found');
        }

        // Рассчитываем total_price, total_cost, profit и total_vat_sum
        $calculatedData = $this->calculateShipmentItemTotals($dto, $shipment);

        // Устанавливаем рассчитанные значения в DTO
        $dto->totalPrice = $calculatedData['total_price'];
        $dto->totalCost = $calculatedData['total_cost'];
        $dto->profit = $calculatedData['profit'];
        $dto->totalVatSum = $calculatedData['total_vat_sum'];

        $this->shipmentItemsRepository->update(
            $dto->resourceId,
            $dto->toUpdateArray(),
        );

        // Обновляем общую сумму отгрузки
        if ($this->compare($item->total_price, $calculatedData['total_price']) != 0) {
            $priceDifference = $this->subtract($item->total_price, $calculatedData['total_price']);
            $newTotalPrice = $this->subtract($shipment->total_price, $priceDifference);
            $this->shipmentsRepository
                ->update(
                    $item->shipment_id,
                    ['total_price' => $newTotalPrice],
                );
        }

        Queue::push(new RecalculationAfterUpdateShipmentItemJob($item, $dto));
    }

    private function calculateShipmentItemTotals(ShipmentItemDTO $dto, object $shipment): array
    {
        // Получаем ставку НДС
        $vatRate = '0';
        if ($dto->vat_rate_id) {
            $vatRateEntity = $this->vatRatesRepository->show($dto->vat_rate_id);
            $vatRate = $vatRateEntity ? (string)$vatRateEntity->rate : '0';
        }

        // Рассчитываем общую стоимость позиции
        $totalPrice = $this->calculateSumPrice([
            'price' => $dto->price,
            'quantity' => (string)$dto->quantity,
            'discount' => $dto->discount,
        ]);

        // Рассчитываем общую себестоимость
        $totalCost = $this->multiply($dto->cost, (string)$dto->quantity);

        // Рассчитываем прибыль
        $profit = $this->subtract($totalPrice, $totalCost);

        // Рассчитываем НДС в зависимости от настроек отгрузки
        $totalVatSum = '0';
        if ($shipment->price_includes_vat && $this->compare($vatRate, '0') > 0) {
            // НДС включен в цену
            $vatCalculation = $this->extractVat($totalPrice, $vatRate);
            $totalVatSum = $vatCalculation['vat_amount'];
        } elseif (!$shipment->price_includes_vat && $this->compare($vatRate, '0') > 0) {
            // НДС сверх цены
            $vatCalculation = $this->calculateVat($totalPrice, $vatRate);
            $totalPrice = $vatCalculation['total_amount'];
            $totalVatSum = $vatCalculation['vat_amount'];
        }

        return [
            'total_price' => $totalPrice,
            'total_cost' => $totalCost,
            'profit' => $profit,
            'total_vat_sum' => $totalVatSum
        ];
    }

    private function calculateSumPrice(array $item): string
    {
        $price = $this->toStringNumber($item['price']);
        $quantity = $this->toStringNumber($item['quantity']);
        $discount = $this->toStringNumber($item['discount'] ?? '0');

        $totalPrice = $this->multiply($price, $quantity);

        if ($this->compare($discount, '0') > 0) {
            $totalPrice = $this->applyDiscount($totalPrice, $discount);
        }

        return $totalPrice;
    }
}
