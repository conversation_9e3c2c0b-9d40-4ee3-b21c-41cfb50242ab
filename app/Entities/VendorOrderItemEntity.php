<?php

namespace App\Entities;

class VendorOrderItemEntity extends BaseEntity
{
    public static string $table = 'vendor_order_items';

    public static array $fields = [
        'id',
        'created_at',
        'updated_at',
        'order_id',
        'product_id',
        'quantity',
        'vat_rate_id',
        'discount',
        'currency_id',
        'price_in_currency',
        'currency_rate_to_base',
        'price_in_base',
        'amount_in_base',
        'total_vat_sum',
        'accepted_quantity',
        'available_quantity',
        'recidual'
    ];

    public function products(): RelationBuilder
    {
        return $this->hasOne(ProductEntity::class, 'product_id', 'id');
    }

    public function cabinet_currencies(): RelationBuilder
    {
        return $this->hasOne(CabinetCurrencyEntity::class, 'currency_id', 'id');
    }

    public function vendor_orders(): RelationBuilder
    {
        return $this->hasOne(VendorOrderEntity::class, 'order_id', 'id');
    }

    public function vat_rates(): RelationBuilder
    {
        return $this->hasOne(VatRateEntity::class, 'vat_rate_id', 'id');
    }
}
